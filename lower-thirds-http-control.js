/**
 * Lower Thirds HTTP Control
 *
 * Node.js functions to control OBS Lower Thirds via HTTP
 * Works with the Python script running in OBS that creates an HTTP server
 */

const http = require('http');

class LowerThirdsController {
    constructor(host = 'localhost', port = 8080) {
        this.host = host;
        this.port = port;
        this.baseUrl = `http://${host}:${port}`;
    }

    /**
     * Make HTTP request to OBS Python script
     */
    async makeRequest(method, data = null) {
        return new Promise((resolve, reject) => {
            const options = {
                hostname: this.host,
                port: this.port,
                path: '/',
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            const req = http.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const result = JSON.parse(responseData);
                        resolve(result);
                    } catch (error) {
                        reject(new Error(`Invalid JSON response: ${responseData}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`HTTP request failed: ${error.message}`));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * Get current state of all Lower Thirds
     */
    async getState() {
        try {
            const result = await this.makeRequest('GET');
            console.log('📊 Current Lower Thirds state:', result);
            return result;
        } catch (error) {
            console.error('❌ Failed to get state:', error.message);
            return null;
        }
    }

    /**
     * Set text for a Lower Third (without changing visibility)
     */
    async setText(number, name, info) {
        try {
            const data = {
                number: number,
                name: name,
                info: info,
                action: 'set'
            };

            const result = await this.makeRequest('POST', data);
            if (result && result.status === 'success') {
                console.log(`📝 Set LT${number} text: "${name}" - "${info}"`);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`❌ Failed to set text for LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Show a Lower Third with text
     */
    async show(number, name = '', info = '') {
        try {
            const data = {
                number: number,
                name: name,
                info: info,
                action: 'show'
            };

            const result = await this.makeRequest('POST', data);
            if (result && result.status === 'success') {
                console.log(`👁️ Showing LT${number}: "${name}" - "${info}"`);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`❌ Failed to show LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Hide a Lower Third
     */
    async hide(number) {
        try {
            const data = {
                number: number,
                action: 'hide'
            };

            const result = await this.makeRequest('POST', data);
            if (result && result.status === 'success') {
                console.log(`🙈 Hiding LT${number}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`❌ Failed to hide LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Toggle a Lower Third visibility
     */
    async toggle(number) {
        try {
            const data = {
                number: number,
                action: 'toggle'
            };

            const result = await this.makeRequest('POST', data);
            if (result && result.status === 'success') {
                console.log(`🔄 Toggling LT${number}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`❌ Failed to toggle LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Test connection to OBS HTTP server
     */
    async testConnection() {
        try {
            const result = await this.getState();
            if (result && result.status === 'ok') {
                console.log('✅ Connection to OBS HTTP server successful');
                console.log('💡 Python script is responding correctly');
                return true;
            } else {
                console.log('❌ Unexpected response from OBS script');
                return false;
            }
        } catch (error) {
            console.error('❌ Connection test failed:', error.message);
            console.log('💡 Make sure:');
            console.log('   1. OBS Studio is running');
            console.log('   2. Python script "lower-thirds-websocket-control.py" is loaded');
            console.log('   3. HTTP server is running on the correct port');
            return false;
        }
    }
}

// Example usage and testing
async function example() {
    console.log('🚀 Lower Thirds HTTP Control Example');
    console.log('');

    const controller = new LowerThirdsController();

    try {
        // Test connection
        const connected = await controller.testConnection();
        if (!connected) {
            return;
        }

        console.log('');
        console.log('📋 Running example sequence...');

        // Example 1: Show Lower Third 1
        await controller.show(1, 'John Doe', 'Software Engineer');
        await sleep(3000);

        // Example 2: Update text without hiding
        await controller.setText(1, 'John Doe', 'Senior Developer');
        await sleep(2000);

        // Example 3: Show Lower Third 2
        await controller.show(2, 'Jane Smith', 'Product Manager');
        await sleep(3000);

        // Example 4: Hide Lower Third 1
        await controller.hide(1);
        await sleep(2000);

        // Example 5: Toggle Lower Third 2
        await controller.toggle(2); // Hide
        await sleep(1000);
        await controller.toggle(2); // Show
        await sleep(2000);

        // Example 6: Hide all
        await controller.hide(1);
        await controller.hide(2);
        await controller.hide(3);
        await controller.hide(4);

        console.log('');
        console.log('✅ Example completed successfully!');

    } catch (error) {
        console.error('❌ Example failed:', error.message);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Export for use as module
module.exports = LowerThirdsController;

// Run example if executed directly
if (require.main === module) {
    example().catch(console.error);
}
