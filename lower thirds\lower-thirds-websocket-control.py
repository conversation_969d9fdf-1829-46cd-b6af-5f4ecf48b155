"""
OBS Python Script: Lower Thirds WebSocket Control
Uses OBS frontend events to listen for custom Lower Third control commands.

This script registers for OBS frontend events and processes custom commands
sent via the OBS scripting interface to update Lower Third content.
"""

import obspython as obs
import json
import time

# Global variables
debug_mode = False

# Lower Thirds state
lower_thirds_state = {
    1: {"name": "", "info": "", "visible": False},
    2: {"name": "", "info": "", "visible": False},
    3: {"name": "", "info": "", "visible": False},
    4: {"name": "", "info": "", "visible": False}
}

# Global functions that can be called from external scripts
def set_lower_third_text(lt_number, name_text, info_text):
    """Set Lower Third text (callable from external scripts)"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} set text name='{name_text}' info='{info_text}'")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        # Update state
        lower_thirds_state[lt_number]['name'] = name_text
        lower_thirds_state[lt_number]['info'] = info_text

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error setting Lower Third text: {str(e)}")
        return False

def show_lower_third(lt_number, name_text='', info_text=''):
    """Show Lower Third with optional text"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} show name='{name_text}' info='{info_text}'")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        # Update state
        if name_text or info_text:
            lower_thirds_state[lt_number]['name'] = name_text
            lower_thirds_state[lt_number]['info'] = info_text

        lower_thirds_state[lt_number]['visible'] = True

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error showing Lower Third: {str(e)}")
        return False

def hide_lower_third(lt_number):
    """Hide Lower Third"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} hide")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        lower_thirds_state[lt_number]['visible'] = False

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error hiding Lower Third: {str(e)}")
        return False

def toggle_lower_third(lt_number):
    """Toggle Lower Third visibility"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} toggle")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        lower_thirds_state[lt_number]['visible'] = not lower_thirds_state[lt_number]['visible']

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error toggling Lower Third: {str(e)}")
        return False

def update_browser_source(lt_number):
    """Update the browser source by writing to the hotkeys.js file"""
    try:
        # Get script path
        script_path = obs.script_path()
        if script_path:
            import os
            hotkeys_file = os.path.join(os.path.dirname(script_path), '..', 'common', 'js', 'hotkeys.js')
            hotkeys_file = os.path.normpath(hotkeys_file)
            
            # Read current hotkeys state (simplified - in real implementation, parse existing file)
            with open(hotkeys_file, 'w') as f:
                # Write basic hotkey states (you'd want to preserve existing values)
                f.write('hotkeyMasterSwitch = 0;\n')
                f.write('hotkeySwitch1 = 0;\n')
                f.write('hotkeySwitch2 = 0;\n')
                f.write('hotkeySwitch3 = 0;\n')
                f.write('hotkeySwitch4 = 0;\n')
                
                # Add all the other hotkey variables (simplified)
                for i in range(1, 5):
                    for j in range(1, 11):
                        f.write(f'hotkeyAlt{i}Slot{j} = 0;\n')
                
                # Add WebSocket override data
                f.write('// WebSocket text overrides\n')
                for i in range(1, 5):
                    state = lower_thirds_state[i]
                    f.write(f'websocketOverride{i}Name = "{state["name"]}";\n')
                    f.write(f'websocketOverride{i}Info = "{state["info"]}";\n')
                    f.write(f'websocketOverride{i}Visible = {str(state["visible"]).lower()};\n')
                
                f.write(f'websocketTimestamp = {int(time.time())};\n')
            
            if debug_mode:
                obs.script_log(obs.LOG_INFO, f"Updated hotkeys.js for LT{lt_number}")
                
    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error updating browser source: {str(e)}")

# Test function
def test_lower_third():
    """Test function to verify the script is working"""
    obs.script_log(obs.LOG_INFO, "Testing Lower Third control...")
    set_lower_third_text(1, "Test Name", "Test Info")
    show_lower_third(1)
    obs.script_log(obs.LOG_INFO, "Test completed")

# OBS Script Interface
def script_description():
    return """Lower Thirds Control Script

Provides functions to control Lower Thirds text from external applications.

Available functions:
- set_lower_third_text(number, name, info)
- show_lower_third(number, name, info)
- hide_lower_third(number)
- toggle_lower_third(number)
- test_lower_third()

These functions can be called from external scripts or via OBS WebSocket
using the CallVendorRequest method with vendor "obs-python".
"""

def script_properties():
    props = obs.obs_properties_create()
    obs.obs_properties_add_bool(props, "debug_mode", "Debug Mode")

    # Add a button to test the functionality
    obs.obs_properties_add_button(props, "test_button", "Test Lower Third 1", test_button_clicked)

    return props

def script_defaults(settings):
    obs.obs_data_set_default_bool(settings, "debug_mode", False)

def script_update(settings):
    global debug_mode
    debug_mode = obs.obs_data_get_bool(settings, "debug_mode")

def script_load(settings):
    obs.script_log(obs.LOG_INFO, "Loading Lower Thirds Control script")
    script_update(settings)

def script_unload():
    obs.script_log(obs.LOG_INFO, "Unloading Lower Thirds Control script")

def test_button_clicked(props, prop):
    """Callback for test button"""
    test_lower_third()
    return True
