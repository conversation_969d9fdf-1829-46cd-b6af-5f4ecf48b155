/**
 * Lower Thirds Direct Control
 * 
 * Simple Node.js functions to control OBS Lower Thirds by writing directly to hotkeys.js
 * This bypasses WebSocket completely and writes directly to the file system
 */

const fs = require('fs');
const path = require('path');

class LowerThirdsDirectController {
    constructor(projectPath = __dirname) {
        this.projectPath = projectPath;
        this.hotkeysFile = path.join(projectPath, 'common', 'js', 'hotkeys.js');
        
        // Lower Thirds state
        this.lowerThirdsState = {
            1: { name: '', info: '', visible: false },
            2: { name: '', info: '', visible: false },
            3: { name: '', info: '', visible: false },
            4: { name: '', info: '', visible: false }
        };
        
        // Ensure directory exists
        this.ensureDirectoryExists();
    }

    ensureDirectoryExists() {
        const dir = path.dirname(this.hotkeysFile);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
    }

    /**
     * Read existing hotkeys file to preserve existing values
     */
    readExistingHotkeys() {
        try {
            if (fs.existsSync(this.hotkeysFile)) {
                const content = fs.readFileSync(this.hotkeysFile, 'utf8');
                return content;
            }
        } catch (error) {
            console.warn('Could not read existing hotkeys file:', error.message);
        }
        return '';
    }

    /**
     * Write updated hotkeys file with WebSocket overrides
     */
    updateHotkeysFile() {
        try {
            const existingContent = this.readExistingHotkeys();
            let content = '';

            // Preserve existing hotkey states if they exist
            if (existingContent) {
                const lines = existingContent.split('\n');
                for (const line of lines) {
                    const trimmed = line.trim();
                    if (trimmed && 
                        !trimmed.startsWith('//') && 
                        !trimmed.includes('websocket') && 
                        !trimmed.includes('Timestamp')) {
                        content += line + '\n';
                    }
                }
            } else {
                // Write basic hotkey states
                content += 'hotkeyMasterSwitch = 0;\n';
                content += 'hotkeySwitch1 = 0;\n';
                content += 'hotkeySwitch2 = 0;\n';
                content += 'hotkeySwitch3 = 0;\n';
                content += 'hotkeySwitch4 = 0;\n';
                
                // Add all the other hotkey variables
                for (let i = 1; i <= 4; i++) {
                    for (let j = 1; j <= 10; j++) {
                        content += `hotkeyAlt${i}Slot${j} = 0;\n`;
                    }
                }
            }

            // Add WebSocket override data
            content += '// WebSocket text overrides\n';
            for (let i = 1; i <= 4; i++) {
                const state = this.lowerThirdsState[i];
                // Escape quotes in the text
                const name = (state.name || '').replace(/"/g, '\\"');
                const info = (state.info || '').replace(/"/g, '\\"');
                content += `websocketOverride${i}Name = "${name}";\n`;
                content += `websocketOverride${i}Info = "${info}";\n`;
                content += `websocketOverride${i}Visible = ${state.visible};\n`;
            }
            
            content += `websocketTimestamp = ${Date.now()};\n`;

            fs.writeFileSync(this.hotkeysFile, content, 'utf8');
            console.log(`✅ Updated hotkeys.js: ${this.hotkeysFile}`);
            return true;

        } catch (error) {
            console.error('❌ Error updating hotkeys file:', error.message);
            return false;
        }
    }

    /**
     * Set text for a Lower Third (without changing visibility)
     */
    setText(number, name, info) {
        try {
            if (number < 1 || number > 4) {
                throw new Error('Lower Third number must be between 1 and 4');
            }

            this.lowerThirdsState[number].name = name || '';
            this.lowerThirdsState[number].info = info || '';

            const success = this.updateHotkeysFile();
            if (success) {
                console.log(`📝 Set LT${number} text: "${name}" - "${info}"`);
            }
            return success;

        } catch (error) {
            console.error(`❌ Failed to set text for LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Show a Lower Third with text
     */
    show(number, name = '', info = '') {
        try {
            if (number < 1 || number > 4) {
                throw new Error('Lower Third number must be between 1 and 4');
            }

            if (name || info) {
                this.lowerThirdsState[number].name = name;
                this.lowerThirdsState[number].info = info;
            }
            this.lowerThirdsState[number].visible = true;

            const success = this.updateHotkeysFile();
            if (success) {
                console.log(`👁️ Showing LT${number}: "${name}" - "${info}"`);
            }
            return success;

        } catch (error) {
            console.error(`❌ Failed to show LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Hide a Lower Third
     */
    hide(number) {
        try {
            if (number < 1 || number > 4) {
                throw new Error('Lower Third number must be between 1 and 4');
            }

            this.lowerThirdsState[number].visible = false;

            const success = this.updateHotkeysFile();
            if (success) {
                console.log(`🙈 Hiding LT${number}`);
            }
            return success;

        } catch (error) {
            console.error(`❌ Failed to hide LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Toggle a Lower Third visibility
     */
    toggle(number) {
        try {
            if (number < 1 || number > 4) {
                throw new Error('Lower Third number must be between 1 and 4');
            }

            this.lowerThirdsState[number].visible = !this.lowerThirdsState[number].visible;

            const success = this.updateHotkeysFile();
            if (success) {
                const action = this.lowerThirdsState[number].visible ? 'Showing' : 'Hiding';
                console.log(`🔄 ${action} LT${number}`);
            }
            return success;

        } catch (error) {
            console.error(`❌ Failed to toggle LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Clear text for a specific Lower Third
     */
    clearText(number) {
        return this.setText(number, '', '');
    }

    /**
     * Hide all Lower Thirds
     */
    hideAll() {
        let success = true;
        for (let i = 1; i <= 4; i++) {
            this.lowerThirdsState[i].visible = false;
        }
        success = this.updateHotkeysFile();
        if (success) {
            console.log('🙈 Hiding all Lower Thirds');
        }
        return success;
    }

    /**
     * Clear all Lower Third texts
     */
    clearAllTexts() {
        let success = true;
        for (let i = 1; i <= 4; i++) {
            this.lowerThirdsState[i].name = '';
            this.lowerThirdsState[i].info = '';
        }
        success = this.updateHotkeysFile();
        if (success) {
            console.log('🧹 Cleared all Lower Third texts');
        }
        return success;
    }

    /**
     * Get current state of all Lower Thirds
     */
    getState() {
        return { ...this.lowerThirdsState };
    }

    /**
     * Test the system
     */
    test() {
        console.log('🧪 Testing Lower Third Direct Control...');
        
        // Test setting text
        this.setText(1, 'Test Name', 'Test Info');
        
        // Test showing
        this.show(1);
        
        console.log('✅ Test completed');
        console.log('💡 Check OBS browser source to see if the text appears');
        
        return true;
    }
}

// Example usage and testing
async function example() {
    console.log('🚀 Lower Thirds Direct Control Example');
    console.log('');
    
    const controller = new LowerThirdsDirectController();
    
    try {
        console.log('📋 Running example sequence...');
        
        // Example 1: Show Lower Third 1
        controller.show(1, 'John Doe', 'Software Engineer');
        await sleep(3000);
        
        // Example 2: Update text without hiding
        controller.setText(1, 'John Doe', 'Senior Developer');
        await sleep(2000);
        
        // Example 3: Show Lower Third 2
        controller.show(2, 'Jane Smith', 'Product Manager');
        await sleep(3000);
        
        // Example 4: Hide Lower Third 1
        controller.hide(1);
        await sleep(2000);
        
        // Example 5: Toggle Lower Third 2
        controller.toggle(2); // Hide
        await sleep(1000);
        controller.toggle(2); // Show
        await sleep(2000);
        
        // Example 6: Hide all
        controller.hideAll();
        
        console.log('');
        console.log('✅ Example completed successfully!');
        console.log('💡 Check your OBS browser source to see the changes');
        
    } catch (error) {
        console.error('❌ Example failed:', error.message);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Export for use as module
module.exports = LowerThirdsDirectController;

// Run example if executed directly
if (require.main === module) {
    example().catch(console.error);
}
