-- <PERSON>ript Lua simple pour changer le texte d'un bandeau Lower Third
-- Ce script modifie directement le fichier hotkeys.js pour changer les textes

-- Configuration : modifiez ces valeurs pour changer le texte
local BANDEAU_NUMERO = 1  -- Quel bandeau modifier (1, 2, 3 ou 4)
local NOUVEAU_NOM = "<PERSON>"
local NOUVELLE_INFO = "Développeur Senior"

-- Fonction pour obtenir le chemin du script
function script_path()
    local str = debug.getinfo(2, "S").source:sub(2)
    return str:match("(.*/)")
end

-- Fonction pour changer le texte
function changer_texte_bandeau()
    obs.script_log(obs.LOG_INFO, "Changement du texte du bandeau " .. BANDEAU_NUMERO)
    
    -- Chemin vers le fichier hotkeys.js
    local hotkeys_file = script_path() .. '../common/js/hotkeys.js'
    
    -- <PERSON>re le fichier existant
    local existing_content = ""
    local file = io.open(hotkeys_file, "r")
    if file then
        existing_content = file:read("*all")
        file:close()
    end
    
    -- C<PERSON>er le nouveau contenu
    local new_content = ""
    
    -- Si le fichier existe, garder les lignes existantes (sauf les overrides WebSocket)
    if existing_content ~= "" then
        for line in existing_content:gmatch("[^\r\n]+") do
            if not line:match("websocket") and not line:match("//") then
                new_content = new_content .. line .. "\n"
            end
        end
    else
        -- Créer un contenu de base si le fichier n'existe pas
        new_content = new_content .. "hotkeyMasterSwitch = 0;\n"
        new_content = new_content .. "hotkeySwitch1 = 0;\n"
        new_content = new_content .. "hotkeySwitch2 = 0;\n"
        new_content = new_content .. "hotkeySwitch3 = 0;\n"
        new_content = new_content .. "hotkeySwitch4 = 0;\n"
        
        -- Ajouter toutes les variables hotkey
        for i = 1, 4 do
            for j = 1, 10 do
                new_content = new_content .. "hotkeyAlt" .. i .. "Slot" .. j .. " = 0;\n"
            end
        end
    end
    
    -- Ajouter les overrides WebSocket
    new_content = new_content .. "// Override WebSocket pour le texte\n"
    for i = 1, 4 do
        if i == BANDEAU_NUMERO then
            new_content = new_content .. "websocketOverride" .. i .. "Name = \"" .. NOUVEAU_NOM .. "\";\n"
            new_content = new_content .. "websocketOverride" .. i .. "Info = \"" .. NOUVELLE_INFO .. "\";\n"
            new_content = new_content .. "websocketOverride" .. i .. "Visible = true;\n"
        else
            new_content = new_content .. "websocketOverride" .. i .. "Name = \"\";\n"
            new_content = new_content .. "websocketOverride" .. i .. "Info = \"\";\n"
            new_content = new_content .. "websocketOverride" .. i .. "Visible = false;\n"
        end
    end
    new_content = new_content .. "websocketTimestamp = " .. os.time() .. ";\n"
    
    -- Écrire le nouveau fichier
    local output = io.open(hotkeys_file, "w")
    if output then
        output:write(new_content)
        output:close()
        obs.script_log(obs.LOG_INFO, "Texte changé avec succès : " .. NOUVEAU_NOM .. " - " .. NOUVELLE_INFO)
    else
        obs.script_log(obs.LOG_ERROR, "Impossible d'écrire dans le fichier : " .. hotkeys_file)
    end
end

-- Interface OBS
function script_description()
    return "Script simple pour changer le texte d'un bandeau Lower Third.\n\nModifiez les variables NOUVEAU_NOM et NOUVELLE_INFO dans le code pour changer le texte."
end

function script_properties()
    local props = obs.obs_properties_create()
    
    -- Bouton pour changer le texte
    obs.obs_properties_add_button(props, "changer_texte", "Changer le texte du bandeau", function()
        changer_texte_bandeau()
        return true
    end)
    
    return props
end

function script_load(settings)
    obs.script_log(obs.LOG_INFO, "Script de changement de texte chargé")
end

function script_unload()
    obs.script_log(obs.LOG_INFO, "Script de changement de texte déchargé")
end
