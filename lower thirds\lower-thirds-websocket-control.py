"""
OBS Python Script: Lower Thirds HTTP Control
Creates a simple HTTP server to listen for Lower Third control commands.

This script creates an HTTP server that external applications can send
POST requests to in order to control Lower Third text and visibility.
"""

import obspython as obs
import json
import time
import threading
import socket
from http.server import H<PERSON>PServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# Global variables
debug_mode = False
http_server = None
server_thread = None
server_port = 8080
running = False

# Lower Thirds state
lower_thirds_state = {
    1: {"name": "", "info": "", "visible": False},
    2: {"name": "", "info": "", "visible": False},
    3: {"name": "", "info": "", "visible": False},
    4: {"name": "", "info": "", "visible": False}
}

# Global functions that can be called from external scripts
def set_lower_third_text(lt_number, name_text, info_text):
    """Set Lower Third text (callable from external scripts)"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} set text name='{name_text}' info='{info_text}'")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        # Update state
        lower_thirds_state[lt_number]['name'] = name_text
        lower_thirds_state[lt_number]['info'] = info_text

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error setting Lower Third text: {str(e)}")
        return False

def show_lower_third(lt_number, name_text='', info_text=''):
    """Show Lower Third with optional text"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} show name='{name_text}' info='{info_text}'")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        # Update state
        if name_text or info_text:
            lower_thirds_state[lt_number]['name'] = name_text
            lower_thirds_state[lt_number]['info'] = info_text

        lower_thirds_state[lt_number]['visible'] = True

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error showing Lower Third: {str(e)}")
        return False

def hide_lower_third(lt_number):
    """Hide Lower Third"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} hide")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        lower_thirds_state[lt_number]['visible'] = False

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error hiding Lower Third: {str(e)}")
        return False

def toggle_lower_third(lt_number):
    """Toggle Lower Third visibility"""
    try:
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Lower Third Control: LT{lt_number} toggle")

        # Validate Lower Third number
        if lt_number not in [1, 2, 3, 4]:
            obs.script_log(obs.LOG_WARNING, f"Invalid Lower Third number: {lt_number}")
            return False

        lower_thirds_state[lt_number]['visible'] = not lower_thirds_state[lt_number]['visible']

        # Update the browser source
        update_browser_source(lt_number)
        return True

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error toggling Lower Third: {str(e)}")
        return False

def update_browser_source(lt_number):
    """Update the browser source by writing to the hotkeys.js file"""
    try:
        import os

        # Try to find the hotkeys.js file in common locations
        possible_paths = [
            # Relative to current working directory
            os.path.join('common', 'js', 'hotkeys.js'),
            os.path.join('..', 'common', 'js', 'hotkeys.js'),
            # Absolute paths that might work
            r'C:\Utile\Animated-Lower-Thirds\common\js\hotkeys.js',
            # Try to get from OBS data directory
            os.path.join(os.path.expanduser('~'), 'obs-studio', 'scripts', '..', '..', 'common', 'js', 'hotkeys.js')
        ]

        hotkeys_file = None
        for path in possible_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(os.path.dirname(abs_path)):
                hotkeys_file = abs_path
                break

        if not hotkeys_file:
            # Create a default path
            hotkeys_file = os.path.abspath(os.path.join('common', 'js', 'hotkeys.js'))
            # Ensure directory exists
            os.makedirs(os.path.dirname(hotkeys_file), exist_ok=True)

        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Writing to hotkeys file: {hotkeys_file}")

        # Read existing hotkeys file to preserve values
        existing_content = ""
        if os.path.exists(hotkeys_file):
            try:
                with open(hotkeys_file, 'r') as f:
                    existing_content = f.read()
            except:
                pass

        # Write updated content
        with open(hotkeys_file, 'w') as f:
            # If we have existing content, preserve the hotkey states
            if existing_content and not existing_content.startswith('//'):
                # Write existing content first (up to WebSocket overrides)
                lines = existing_content.split('\n')
                for line in lines:
                    if line.strip() and not line.strip().startswith('//') and not 'websocket' in line.lower():
                        f.write(line + '\n')
            else:
                # Write basic hotkey states
                f.write('hotkeyMasterSwitch = 0;\n')
                f.write('hotkeySwitch1 = 0;\n')
                f.write('hotkeySwitch2 = 0;\n')
                f.write('hotkeySwitch3 = 0;\n')
                f.write('hotkeySwitch4 = 0;\n')

                # Add all the other hotkey variables
                for i in range(1, 5):
                    for j in range(1, 11):
                        f.write(f'hotkeyAlt{i}Slot{j} = 0;\n')

            # Add WebSocket override data
            f.write('// WebSocket text overrides\n')
            for i in range(1, 5):
                state = lower_thirds_state[i]
                # Escape quotes in the text
                name = state["name"].replace('"', '\\"') if state["name"] else ""
                info = state["info"].replace('"', '\\"') if state["info"] else ""
                f.write(f'websocketOverride{i}Name = "{name}";\n')
                f.write(f'websocketOverride{i}Info = "{info}";\n')
                f.write(f'websocketOverride{i}Visible = {str(state["visible"]).lower()};\n')

            f.write(f'websocketTimestamp = {int(time.time())};\n')

        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"Successfully updated hotkeys.js for LT{lt_number}")

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error updating browser source: {str(e)}")
        if debug_mode:
            import traceback
            obs.script_log(obs.LOG_ERROR, f"Traceback: {traceback.format_exc()}")

# Test function
def test_lower_third():
    """Test function to verify the script is working"""
    obs.script_log(obs.LOG_INFO, "Testing Lower Third control...")
    set_lower_third_text(1, "Test Name", "Test Info")
    show_lower_third(1)
    obs.script_log(obs.LOG_INFO, "Test completed")

class LowerThirdsHTTPHandler(BaseHTTPRequestHandler):
    """HTTP request handler for Lower Third control"""

    def log_message(self, format, *args):
        """Override to use OBS logging"""
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"HTTP: {format % args}")

    def do_GET(self):
        """Handle GET requests - return current state"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        response = {
            "status": "ok",
            "lower_thirds": lower_thirds_state,
            "message": "OBS Lower Thirds HTTP Control is running"
        }
        self.wfile.write(json.dumps(response).encode())

    def do_POST(self):
        """Handle POST requests - update Lower Thirds"""
        try:
            # Parse the request
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
            else:
                data = {}

            # Extract parameters
            lt_number = data.get('number', 1)
            name_text = data.get('name', '')
            info_text = data.get('info', '')
            action = data.get('action', 'set')

            if debug_mode:
                obs.script_log(obs.LOG_INFO, f"HTTP Request: LT{lt_number} action={action} name='{name_text}' info='{info_text}'")

            # Process the command
            result = False
            if action == 'set':
                result = set_lower_third_text(lt_number, name_text, info_text)
            elif action == 'show':
                result = show_lower_third(lt_number, name_text, info_text)
            elif action == 'hide':
                result = hide_lower_third(lt_number)
            elif action == 'toggle':
                result = toggle_lower_third(lt_number)

            # Send response
            self.send_response(200 if result else 400)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            response = {
                "status": "success" if result else "error",
                "lower_third": lt_number,
                "action": action,
                "data": lower_thirds_state.get(lt_number, {})
            }
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            obs.script_log(obs.LOG_ERROR, f"Error processing HTTP request: {str(e)}")
            self.send_error(500, f"Internal server error: {str(e)}")

    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def start_http_server():
    """Start the HTTP server in a separate thread"""
    global http_server, server_thread, running

    try:
        running = True

        def run_server():
            global http_server
            try:
                http_server = HTTPServer(('localhost', server_port), LowerThirdsHTTPHandler)
                obs.script_log(obs.LOG_INFO, f"HTTP server started on http://localhost:{server_port}")

                while running:
                    http_server.handle_request()

            except Exception as e:
                obs.script_log(obs.LOG_ERROR, f"HTTP server error: {str(e)}")

        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()

        obs.script_log(obs.LOG_INFO, "Lower Thirds HTTP server thread started")

    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Failed to start HTTP server: {str(e)}")

def stop_http_server():
    """Stop the HTTP server"""
    global http_server, server_thread, running

    running = False

    if http_server:
        try:
            http_server.server_close()
        except:
            pass
        http_server = None
        obs.script_log(obs.LOG_INFO, "HTTP server stopped")

    if server_thread:
        server_thread.join(timeout=1)
        server_thread = None

# OBS Script Interface
def script_description():
    return """Lower Thirds Control Script

Provides functions to control Lower Thirds text from external applications.

Available functions:
- set_lower_third_text(number, name, info)
- show_lower_third(number, name, info)
- hide_lower_third(number)
- toggle_lower_third(number)
- test_lower_third()

These functions can be called from external scripts or via OBS WebSocket
using the CallVendorRequest method with vendor "obs-python".
"""

def script_properties():
    props = obs.obs_properties_create()
    obs.obs_properties_add_int(props, "server_port", "HTTP Server Port", 8080, 65535, 1)
    obs.obs_properties_add_bool(props, "debug_mode", "Debug Mode")

    # Add a button to test the functionality
    obs.obs_properties_add_button(props, "test_button", "Test Lower Third 1", test_button_clicked)

    return props

def script_defaults(settings):
    obs.obs_data_set_default_int(settings, "server_port", 8080)
    obs.obs_data_set_default_bool(settings, "debug_mode", False)

def script_update(settings):
    global debug_mode, server_port

    new_port = obs.obs_data_get_int(settings, "server_port")
    debug_mode = obs.obs_data_get_bool(settings, "debug_mode")

    if new_port != server_port:
        server_port = new_port
        # Restart server with new port
        stop_http_server()
        start_http_server()

def script_load(settings):
    obs.script_log(obs.LOG_INFO, "Loading Lower Thirds HTTP Control script")
    script_update(settings)
    start_http_server()

def script_unload():
    obs.script_log(obs.LOG_INFO, "Unloading Lower Thirds HTTP Control script")
    stop_http_server()

def test_button_clicked(props, prop):
    """Callback for test button"""
    # Suppress unused parameter warnings
    _ = props, prop
    test_lower_third()
    return True
