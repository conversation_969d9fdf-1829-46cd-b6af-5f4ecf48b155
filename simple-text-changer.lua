-- Script Lua simple pour changer le texte d'un bandeau Lower Third
-- Ce script utilise le système BroadcastChannel comme le plugin original

-- Configuration : modifiez ces valeurs pour changer le texte
local BANDEAU_NUMERO = 1  -- Quel bandeau modifier (1, 2, 3 ou 4)
local NOUVEAU_NOM = "<PERSON>"
local NOUVELLE_INFO = "Développeur Senior"

-- Fonction pour obtenir le chemin du script
function script_path()
    local str = debug.getinfo(2, "S").source:sub(2)
    return str:match("(.*/)")
end

-- Fonction pour changer le texte via BroadcastChannel
function changer_texte_bandeau()
    print("Changement du texte du bandeau " .. BANDEAU_NUMERO)

    -- Créer un fichier HTML temporaire qui enverra le message via BroadcastChannel
    local temp_file = script_path() .. '../common/js/temp_broadcast.html'

    local html_content = [[
<!DOCTYPE html>
<html>
<head>
    <title>Broadcast Message</title>
</head>
<body>
<script>
// Envoyer le message via BroadcastChannel comme le panneau de contrôle
const bc = new BroadcastChannel('obs-lower-thirds-channel');

// Créer le message avec les nouveaux textes
const message = {
    websocket_override: true,
    alt_]] .. BANDEAU_NUMERO .. [[_name: "]] .. NOUVEAU_NOM .. [[",
    alt_]] .. BANDEAU_NUMERO .. [[_info: "]] .. NOUVELLE_INFO .. [[",
    alt_]] .. BANDEAU_NUMERO .. [[_switch: "true",
    timestamp: ]] .. os.time() .. [[
};

// Envoyer le message
bc.postMessage(message);
console.log('Message envoyé via BroadcastChannel:', message);

// Fermer automatiquement après envoi
setTimeout(() => {
    window.close();
}, 100);
</script>
</body>
</html>]]

    -- Écrire le fichier HTML temporaire
    local output = io.open(temp_file, "w")
    if output then
        output:write(html_content)
        output:close()
        print("Fichier HTML temporaire créé : " .. temp_file)
        print("Texte changé : " .. NOUVEAU_NOM .. " - " .. NOUVELLE_INFO)
        print("IMPORTANT: Ouvrez le fichier " .. temp_file .. " dans un navigateur pour envoyer le message")
    else
        print("ERREUR: Impossible de créer le fichier temporaire")
    end
end

-- Interface OBS (simplifiée pour éviter les erreurs)
function script_description()
    return "Script simple pour changer le texte d'un bandeau Lower Third.\n\nModifiez les variables NOUVEAU_NOM et NOUVELLE_INFO dans le code, puis exécutez la fonction changer_texte_bandeau()."
end

function script_load(settings)
    print("Script de changement de texte chargé")
    print("Pour changer le texte, modifiez les variables en haut du script et appelez changer_texte_bandeau()")
end

function script_unload()
    print("Script de changement de texte déchargé")
end

-- Fonction de test - appelez cette fonction pour changer le texte
function test_changement()
    changer_texte_bandeau()
end
