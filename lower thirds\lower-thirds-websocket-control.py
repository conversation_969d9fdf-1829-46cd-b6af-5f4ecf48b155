"""
OBS Python Script: Lower Thirds WebSocket Control
Provides HTTP/WebSocket server to control Lower Thirds text from external applications.

This script creates a simple HTTP server that listens for POST requests
to update Lower Third text content and visibility.
"""

import obspython as obs
import threading
import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# Global variables
http_server = None
server_thread = None
server_port = 8080
debug_mode = False

# Lower Thirds state
lower_thirds_state = {
    1: {"name": "", "info": "", "visible": False},
    2: {"name": "", "info": "", "visible": False},
    3: {"name": "", "info": "", "visible": False},
    4: {"name": "", "info": "", "visible": False}
}

class LowerThirdsHandler(BaseHTTPRequestHandler):
    """HTTP request handler for Lower Thirds control"""
    
    def log_message(self, format, *args):
        """Override to use OBS logging"""
        if debug_mode:
            obs.script_log(obs.LOG_INFO, f"HTTP: {format % args}")
    
    def do_GET(self):
        """Handle GET requests - return current state"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {
            "status": "ok",
            "lower_thirds": lower_thirds_state
        }
        self.wfile.write(json.dumps(response).encode())
    
    def do_POST(self):
        """Handle POST requests - update Lower Thirds"""
        try:
            # Parse the request
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            # Extract parameters
            lt_number = data.get('number', 1)
            name_text = data.get('name', '')
            info_text = data.get('info', '')
            action = data.get('action', 'set')  # 'set', 'show', 'hide', 'toggle'
            
            if debug_mode:
                obs.script_log(obs.LOG_INFO, f"WebSocket Control: LT{lt_number} action={action} name='{name_text}' info='{info_text}'")
            
            # Validate Lower Third number
            if lt_number not in [1, 2, 3, 4]:
                self.send_error(400, "Invalid Lower Third number (must be 1-4)")
                return
            
            # Update state
            if action in ['set', 'show']:
                lower_thirds_state[lt_number]['name'] = name_text
                lower_thirds_state[lt_number]['info'] = info_text
            
            if action == 'show':
                lower_thirds_state[lt_number]['visible'] = True
            elif action == 'hide':
                lower_thirds_state[lt_number]['visible'] = False
            elif action == 'toggle':
                lower_thirds_state[lt_number]['visible'] = not lower_thirds_state[lt_number]['visible']
            
            # Update the browser source via BroadcastChannel
            update_browser_source(lt_number)
            
            # Send response
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {
                "status": "success",
                "lower_third": lt_number,
                "action": action,
                "data": lower_thirds_state[lt_number]
            }
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            obs.script_log(obs.LOG_ERROR, f"Error processing request: {str(e)}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def update_browser_source(lt_number):
    """Update the browser source by writing to the hotkeys.js file"""
    try:
        # Get script path
        script_path = obs.script_path()
        if script_path:
            import os
            hotkeys_file = os.path.join(os.path.dirname(script_path), '..', 'common', 'js', 'hotkeys.js')
            hotkeys_file = os.path.normpath(hotkeys_file)
            
            # Read current hotkeys state (simplified - in real implementation, parse existing file)
            with open(hotkeys_file, 'w') as f:
                # Write basic hotkey states (you'd want to preserve existing values)
                f.write('hotkeyMasterSwitch = 0;\n')
                f.write('hotkeySwitch1 = 0;\n')
                f.write('hotkeySwitch2 = 0;\n')
                f.write('hotkeySwitch3 = 0;\n')
                f.write('hotkeySwitch4 = 0;\n')
                
                # Add all the other hotkey variables (simplified)
                for i in range(1, 5):
                    for j in range(1, 11):
                        f.write(f'hotkeyAlt{i}Slot{j} = 0;\n')
                
                # Add WebSocket override data
                f.write('// WebSocket text overrides\n')
                for i in range(1, 5):
                    state = lower_thirds_state[i]
                    f.write(f'websocketOverride{i}Name = "{state["name"]}";\n')
                    f.write(f'websocketOverride{i}Info = "{state["info"]}";\n')
                    f.write(f'websocketOverride{i}Visible = {str(state["visible"]).lower()};\n')
                
                f.write(f'websocketTimestamp = {int(time.time())};\n')
            
            if debug_mode:
                obs.script_log(obs.LOG_INFO, f"Updated hotkeys.js for LT{lt_number}")
                
    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Error updating browser source: {str(e)}")

def start_http_server():
    """Start the HTTP server in a separate thread"""
    global http_server, server_thread
    
    try:
        http_server = HTTPServer(('localhost', server_port), LowerThirdsHandler)
        server_thread = threading.Thread(target=http_server.serve_forever, daemon=True)
        server_thread.start()
        
        obs.script_log(obs.LOG_INFO, f"Lower Thirds HTTP server started on http://localhost:{server_port}")
        obs.script_log(obs.LOG_INFO, "API endpoints:")
        obs.script_log(obs.LOG_INFO, "  GET  / - Get current state")
        obs.script_log(obs.LOG_INFO, "  POST / - Update Lower Third")
        obs.script_log(obs.LOG_INFO, "Example: curl -X POST http://localhost:8080 -H 'Content-Type: application/json' -d '{\"number\":1,\"name\":\"John Doe\",\"info\":\"Engineer\",\"action\":\"show\"}'")
        
    except Exception as e:
        obs.script_log(obs.LOG_ERROR, f"Failed to start HTTP server: {str(e)}")

def stop_http_server():
    """Stop the HTTP server"""
    global http_server, server_thread
    
    if http_server:
        http_server.shutdown()
        http_server = None
        obs.script_log(obs.LOG_INFO, "Lower Thirds HTTP server stopped")
    
    if server_thread:
        server_thread.join(timeout=1)
        server_thread = None

# OBS Script Interface
def script_description():
    return """Lower Thirds WebSocket Control

Creates an HTTP server to control Lower Thirds text from external applications.

Server runs on http://localhost:8080

API Usage:
POST / with JSON body:
{
  "number": 1,           // Lower Third number (1-4)
  "name": "John Doe",    // Name text
  "info": "Engineer",    // Info text  
  "action": "show"       // Action: "set", "show", "hide", "toggle"
}

GET / returns current state of all Lower Thirds.
"""

def script_properties():
    props = obs.obs_properties_create()
    obs.obs_properties_add_int(props, "server_port", "Server Port", 8080, 65535, 1)
    obs.obs_properties_add_bool(props, "debug_mode", "Debug Mode")
    return props

def script_defaults(settings):
    obs.obs_data_set_default_int(settings, "server_port", 8080)
    obs.obs_data_set_default_bool(settings, "debug_mode", False)

def script_update(settings):
    global server_port, debug_mode
    
    new_port = obs.obs_data_get_int(settings, "server_port")
    debug_mode = obs.obs_data_get_bool(settings, "debug_mode")
    
    if new_port != server_port:
        server_port = new_port
        # Restart server with new port
        stop_http_server()
        start_http_server()

def script_load(settings):
    obs.script_log(obs.LOG_INFO, "Loading Lower Thirds WebSocket Control script")
    script_update(settings)
    start_http_server()

def script_unload():
    obs.script_log(obs.LOG_INFO, "Unloading Lower Thirds WebSocket Control script")
    stop_http_server()
