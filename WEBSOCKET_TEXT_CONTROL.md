# Contrôle des textes Lower Thirds via HTTP/WebSocket

Ce système permet de contrôler les textes des bandeaux (Lower Thirds) via HTTP depuis une application externe.

## 🆕 Approche finale : HTTP Server Python

Le système utilise maintenant un **script Python dans OBS** qui crée un serveur HTTP local. Cette approche résout tous les problèmes de communication WebSocket et permet un contrôle depuis n'importe quel PC du réseau.

## 🔧 Installation et Configuration

### 1. Prérequis
- OBS Studio (version récente avec support Python)
- Node.js (pour les exemples d'utilisation)

### 2. Configuration OBS
1. Ouvrir OBS Studio
2. Aller dans **Outils** → **Scripts**
3. Ajouter le script `lower thirds/lower-thirds-websocket-control.py`
4. Configurer le port du serveur HTTP (défaut: 8080)
5. Activer le mode debug si nécessaire
6. Cliquer sur "Test Lower Third 1" pour vérifier que ça fonctionne

### 3. Configuration du serveur HTTP
- **Adresse** : `http://localhost:8080` (ou IP du PC OBS si distant)
- **Port** : 8080 (configurable dans les propriétés du script)
- **API** : REST JSON
- **CORS** : Activé pour les appels cross-origin

## 📝 API HTTP disponible

### Endpoints

#### `GET /`
Récupère l'état actuel de tous les Lower Thirds.
```bash
curl http://localhost:8080
```

#### `POST /`
Met à jour un Lower Third avec JSON.

**Paramètres :**
- `number` : Numéro du bandeau (1-4)
- `name` : Texte du nom
- `info` : Texte d'information
- `action` : Action à effectuer
  - `"set"` : Définir le texte sans changer la visibilité
  - `"show"` : Afficher avec le texte
  - `"hide"` : Masquer
  - `"toggle"` : Basculer la visibilité

**Exemples :**
```bash
# Afficher un bandeau
curl -X POST http://localhost:8080 \
  -H "Content-Type: application/json" \
  -d '{"number":1,"name":"John Doe","info":"Engineer","action":"show"}'

# Masquer un bandeau
curl -X POST http://localhost:8080 \
  -H "Content-Type: application/json" \
  -d '{"number":1,"action":"hide"}'
```

### Fonctions avancées

#### `set_lower_third_name(number, name)`
Met à jour uniquement le nom d'un bandeau.
```javascript
await controller.setName(1, "Dr. John Doe");
```

#### `set_lower_third_info(number, info)`
Met à jour uniquement l'info d'un bandeau.
```javascript
await controller.setInfo(1, "Chief Technology Officer");
```

#### `clear_lower_third_text(number)`
Efface le texte d'un bandeau spécifique.
```javascript
await controller.clearText(1);
```

#### `hide_all_lower_thirds()`
Masque tous les bandeaux.
```javascript
await controller.hideAll();
```

#### `clear_all_lower_third_texts()`
Efface tous les textes des bandeaux.
```javascript
await controller.clearAllTexts();
```

## 🚀 Utilisation avec Node.js

### Installation des dépendances
```bash
npm install obs-websocket-js
```

### Exemple d'utilisation
```javascript
const LowerThirdController = require('./websocket-text-control-example.js');

async function main() {
    const controller = new LowerThirdController();
    
    try {
        // Connexion à OBS
        await controller.connect('ws://localhost:4455', '');
        
        // Afficher un bandeau
        await controller.show(1, 'Nom de la personne', 'Titre/Fonction');
        
        // Attendre 5 secondes
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Masquer le bandeau
        await controller.hide(1);
        
    } finally {
        await controller.disconnect();
    }
}

main().catch(console.error);
```

## 🔍 Comment ça fonctionne

### Mémorisation des textes
Les textes sont mémorisés dans le **localStorage** du navigateur via le panneau de contrôle. C'est pourquoi ils persistent même après fermeture/réouverture d'OBS.

### Flux de données
1. **Application externe** → WebSocket → **Script Lua OBS**
2. **Script Lua** → Fichier `websocket_texts.js` → **Browser Source**
3. **Browser Source** → Affichage des bandeaux avec les nouveaux textes

### Fichiers impliqués
- `lower-thirds_hotkeys.lua` : Script Lua avec les fonctions WebSocket
- `common/js/websocket_texts.js` : Fichier de données généré automatiquement
- `lower thirds/browser-source.html` : Source navigateur qui affiche les bandeaux
- `lower thirds/control-panel.html` : Panneau de contrôle (stockage localStorage)

## 🐛 Dépannage

### Les textes ne s'affichent pas
1. Vérifier que le script Lua est chargé dans OBS
2. Vérifier que le WebSocket est activé et accessible
3. Vérifier que le fichier `websocket_texts.js` est créé dans `common/js/`
4. Vérifier les logs OBS pour les erreurs Lua

### Les textes ne persistent pas
- Les textes WebSocket sont temporaires et remplacent les textes du panneau de contrôle
- Pour une persistance permanente, utiliser le panneau de contrôle d'OBS

### Erreurs de connexion WebSocket
1. Vérifier l'adresse et le port (défaut: `ws://localhost:4455`)
2. Vérifier le mot de passe WebSocket dans OBS
3. S'assurer qu'OBS est en cours d'exécution

## 📋 Numéros des bandeaux
- **1** : Lower Third 1
- **2** : Lower Third 2  
- **3** : Lower Third 3
- **4** : Lower Third 4

## 🎯 Cas d'usage

### Streaming en direct
```javascript
// Présenter un invité
await controller.show(1, "Marie Dupont", "Experte en IA");

// Changer le titre pendant l'interview
await controller.setInfo(1, "Auteure de 'IA et Société'");

// Masquer à la fin
await controller.hide(1);
```

### Présentation automatisée
```javascript
const speakers = [
    { name: "Jean Martin", title: "CEO" },
    { name: "Sophie Durand", title: "CTO" },
    { name: "Pierre Moreau", title: "Designer" }
];

for (let i = 0; i < speakers.length; i++) {
    await controller.show(1, speakers[i].name, speakers[i].title);
    await new Promise(resolve => setTimeout(resolve, 10000)); // 10 secondes
    await controller.hide(1);
}
```
