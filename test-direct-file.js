/**
 * Direct file test for Lower Third text control
 * 
 * This script directly writes to the websocket_texts.js file
 * to test the text update system without requiring WebSocket connection.
 * 
 * Usage: node test-direct-file.js
 */

const fs = require('fs');
const path = require('path');

class DirectLowerThirdController {
    constructor() {
        this.textFilePath = path.join(__dirname, 'common', 'js', 'websocket_texts.js');
        this.ensureDirectoryExists();
    }

    ensureDirectoryExists() {
        const dir = path.dirname(this.textFilePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
    }

    /**
     * Set text for a specific Lower Third
     */
    setText(lowerThirdNumber, nameText, infoText) {
        console.log(`📝 Setting LT${lowerThirdNumber} text: "${nameText}" - "${infoText}"`);
        
        const content = `// Lower Thirds texts set via WebSocket
// This file is automatically generated by the Lua script
window.websocketTexts = window.websocketTexts || {};
window.websocketTexts.alt_${lowerThirdNumber}_name = "${nameText || ''}";
window.websocketTexts.alt_${lowerThirdNumber}_info = "${infoText || ''}";
window.websocketTexts.timestamp = ${Date.now()};
window.websocketTexts.updated_lt = ${lowerThirdNumber};
`;

        try {
            fs.writeFileSync(this.textFilePath, content, 'utf8');
            console.log(`✅ Text file updated: ${this.textFilePath}`);
            return true;
        } catch (error) {
            console.error(`❌ Error writing text file:`, error.message);
            return false;
        }
    }

    /**
     * Read current texts from file
     */
    getCurrentTexts() {
        try {
            if (!fs.existsSync(this.textFilePath)) {
                return null;
            }

            const content = fs.readFileSync(this.textFilePath, 'utf8');
            console.log('📖 Current websocket_texts.js content:');
            console.log(content);
            return content;
        } catch (error) {
            console.error('❌ Error reading text file:', error.message);
            return null;
        }
    }

    /**
     * Clear text for a specific Lower Third
     */
    clearText(lowerThirdNumber) {
        console.log(`🧹 Clearing LT${lowerThirdNumber} text`);
        return this.setText(lowerThirdNumber, '', '');
    }

    /**
     * Sleep helper function
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

async function runDirectTest() {
    console.log('🚀 Starting Direct File Test for Lower Third Text Control');
    console.log('📁 This test writes directly to common/js/websocket_texts.js');
    console.log('');

    const controller = new DirectLowerThirdController();

    try {
        // Test 1: Set text for Lower Third 1
        console.log('\n📝 Test 1: Setting text for Lower Third 1');
        controller.setText(1, 'John Doe', 'Software Engineer');
        controller.getCurrentTexts();
        await controller.sleep(2000);

        // Test 2: Update Lower Third 2
        console.log('\n📝 Test 2: Setting text for Lower Third 2');
        controller.setText(2, 'Jane Smith', 'Product Manager');
        controller.getCurrentTexts();
        await controller.sleep(2000);

        // Test 3: Update Lower Third 1 with new info
        console.log('\n📝 Test 3: Updating Lower Third 1 with new info');
        controller.setText(1, 'John Doe', 'Senior Developer');
        controller.getCurrentTexts();
        await controller.sleep(2000);

        // Test 4: Set text for Lower Third 3
        console.log('\n📝 Test 4: Setting text for Lower Third 3');
        controller.setText(3, 'Alice Johnson', 'UX Designer');
        controller.getCurrentTexts();
        await controller.sleep(2000);

        // Test 5: Clear text for Lower Third 2
        console.log('\n🧹 Test 5: Clearing text for Lower Third 2');
        controller.clearText(2);
        controller.getCurrentTexts();
        await controller.sleep(2000);

        // Test 6: Set text for Lower Third 4
        console.log('\n📝 Test 6: Setting text for Lower Third 4');
        controller.setText(4, 'Bob Wilson', 'DevOps Engineer');
        controller.getCurrentTexts();

        console.log('\n✅ All direct file tests completed!');
        console.log('\n📋 Next steps:');
        console.log('1. Open OBS Studio');
        console.log('2. Load the lower-thirds_hotkeys.lua script');
        console.log('3. Add the browser source (lower thirds/browser-source.html)');
        console.log('4. The text should update automatically in the browser source');
        console.log('5. You can also open the control panel to see the interface');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Export for use as module
module.exports = DirectLowerThirdController;

// Run test if executed directly
if (require.main === module) {
    runDirectTest().catch(console.error);
}
