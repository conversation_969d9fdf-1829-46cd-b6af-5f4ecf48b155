# Contrôle des textes Lower Thirds via WebSocket

Ce système permet de contrôler les textes des bandeaux (Lower Thirds) via WebSocket depuis une application externe.

## 🔧 Installation et Configuration

### 1. Prérequis
- OBS Studio avec le plugin WebSocket activé
- Le script Lua `lower-thirds_hotkeys.lua` chargé dans OBS
- Node.js (pour les exemples)

### 2. Configuration OBS
1. Ouvrir OBS Studio
2. Aller dans **Outils** → **Scripts**
3. Ajouter le script `lower thirds/lower-thirds_hotkeys.lua`
4. Activer le plugin WebSocket dans **Outils** → **WebSocket Server Settings**

### 3. Configuration WebSocket
- **Adresse par défaut** : `ws://localhost:4455`
- **Mot de passe** : (optionnel, configurable dans OBS)

## 📝 Fonctions disponibles

### Fonctions principales

#### `set_lower_third_text(number, name, info)`
Définit le nom et l'info d'un bandeau.
```javascript
await controller.setText(1, "<PERSON>", "Software Engineer");
```

#### `show_lower_third(number, name, info)`
Affiche un bandeau avec le texte spécifié.
```javascript
await controller.show(1, "Jane Smith", "Product Manager");
```

#### `hide_lower_third(number)`
Masque un bandeau spécifique.
```javascript
await controller.hide(1);
```

#### `toggle_lower_third(number, enabled)`
Active/désactive un bandeau.
```javascript
await controller.toggle(1, true);  // Afficher
await controller.toggle(1, false); // Masquer
```

### Fonctions avancées

#### `set_lower_third_name(number, name)`
Met à jour uniquement le nom d'un bandeau.
```javascript
await controller.setName(1, "Dr. John Doe");
```

#### `set_lower_third_info(number, info)`
Met à jour uniquement l'info d'un bandeau.
```javascript
await controller.setInfo(1, "Chief Technology Officer");
```

#### `clear_lower_third_text(number)`
Efface le texte d'un bandeau spécifique.
```javascript
await controller.clearText(1);
```

#### `hide_all_lower_thirds()`
Masque tous les bandeaux.
```javascript
await controller.hideAll();
```

#### `clear_all_lower_third_texts()`
Efface tous les textes des bandeaux.
```javascript
await controller.clearAllTexts();
```

## 🚀 Utilisation avec Node.js

### Installation des dépendances
```bash
npm install obs-websocket-js
```

### Exemple d'utilisation
```javascript
const LowerThirdController = require('./websocket-text-control-example.js');

async function main() {
    const controller = new LowerThirdController();
    
    try {
        // Connexion à OBS
        await controller.connect('ws://localhost:4455', '');
        
        // Afficher un bandeau
        await controller.show(1, 'Nom de la personne', 'Titre/Fonction');
        
        // Attendre 5 secondes
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Masquer le bandeau
        await controller.hide(1);
        
    } finally {
        await controller.disconnect();
    }
}

main().catch(console.error);
```

## 🔍 Comment ça fonctionne

### Mémorisation des textes
Les textes sont mémorisés dans le **localStorage** du navigateur via le panneau de contrôle. C'est pourquoi ils persistent même après fermeture/réouverture d'OBS.

### Flux de données
1. **Application externe** → WebSocket → **Script Lua OBS**
2. **Script Lua** → Fichier `websocket_texts.js` → **Browser Source**
3. **Browser Source** → Affichage des bandeaux avec les nouveaux textes

### Fichiers impliqués
- `lower-thirds_hotkeys.lua` : Script Lua avec les fonctions WebSocket
- `common/js/websocket_texts.js` : Fichier de données généré automatiquement
- `lower thirds/browser-source.html` : Source navigateur qui affiche les bandeaux
- `lower thirds/control-panel.html` : Panneau de contrôle (stockage localStorage)

## 🐛 Dépannage

### Les textes ne s'affichent pas
1. Vérifier que le script Lua est chargé dans OBS
2. Vérifier que le WebSocket est activé et accessible
3. Vérifier que le fichier `websocket_texts.js` est créé dans `common/js/`
4. Vérifier les logs OBS pour les erreurs Lua

### Les textes ne persistent pas
- Les textes WebSocket sont temporaires et remplacent les textes du panneau de contrôle
- Pour une persistance permanente, utiliser le panneau de contrôle d'OBS

### Erreurs de connexion WebSocket
1. Vérifier l'adresse et le port (défaut: `ws://localhost:4455`)
2. Vérifier le mot de passe WebSocket dans OBS
3. S'assurer qu'OBS est en cours d'exécution

## 📋 Numéros des bandeaux
- **1** : Lower Third 1
- **2** : Lower Third 2  
- **3** : Lower Third 3
- **4** : Lower Third 4

## 🎯 Cas d'usage

### Streaming en direct
```javascript
// Présenter un invité
await controller.show(1, "Marie Dupont", "Experte en IA");

// Changer le titre pendant l'interview
await controller.setInfo(1, "Auteure de 'IA et Société'");

// Masquer à la fin
await controller.hide(1);
```

### Présentation automatisée
```javascript
const speakers = [
    { name: "Jean Martin", title: "CEO" },
    { name: "Sophie Durand", title: "CTO" },
    { name: "Pierre Moreau", title: "Designer" }
];

for (let i = 0; i < speakers.length; i++) {
    await controller.show(1, speakers[i].name, speakers[i].title);
    await new Promise(resolve => setTimeout(resolve, 10000)); // 10 secondes
    await controller.hide(1);
}
```
