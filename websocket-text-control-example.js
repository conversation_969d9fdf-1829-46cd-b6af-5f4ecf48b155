/**
 * Example Node.js script to control Lower Third texts via OBS WebSocket
 * 
 * This script demonstrates how to:
 * - Connect to OBS WebSocket
 * - Set text for Lower Thirds
 * - Show/hide Lower Thirds with custom text
 * 
 * Prerequisites:
 * - OBS Studio with WebSocket plugin enabled
 * - The modified Lua script loaded in OBS
 * - npm install obs-websocket-js
 */

const OBSWebSocket = require('obs-websocket-js').default;

class LowerThirdController {
    constructor() {
        this.obs = new OBSWebSocket();
        this.connected = false;
    }

    async connect(address = 'ws://localhost:4455', password = '') {
        try {
            await this.obs.connect(address, password);
            this.connected = true;
            console.log('✅ Connected to OBS WebSocket');
            
            // Listen for connection events
            this.obs.on('ConnectionClosed', () => {
                this.connected = false;
                console.log('❌ OBS WebSocket connection closed');
            });

        } catch (error) {
            console.error('❌ Failed to connect to OBS:', error.message);
            throw error;
        }
    }

    async disconnect() {
        if (this.connected) {
            await this.obs.disconnect();
            this.connected = false;
            console.log('🔌 Disconnected from OBS WebSocket');
        }
    }

    /**
     * Call a Lua function in OBS
     */
    async callLuaFunction(functionName, ...args) {
        if (!this.connected) {
            throw new Error('Not connected to OBS');
        }

        try {
            // Create the Lua script call
            const luaScript = `${functionName}(${args.map(arg => 
                typeof arg === 'string' ? `"${arg.replace(/"/g, '\\"')}"` : arg
            ).join(', ')})`;

            console.log(`📞 Calling Lua function: ${luaScript}`);

            // Execute the Lua script
            await this.obs.call('CallVendorRequest', {
                vendorName: 'obs-lua',
                requestType: 'call_function',
                requestData: {
                    script_name: 'lower-thirds_hotkeys.lua',
                    function_call: luaScript
                }
            });

            return true;
        } catch (error) {
            console.error(`❌ Error calling Lua function ${functionName}:`, error.message);
            return false;
        }
    }

    /**
     * Set text for a specific Lower Third (1-4)
     */
    async setText(lowerThirdNumber, nameText, infoText) {
        console.log(`📝 Setting LT${lowerThirdNumber} text: "${nameText}" - "${infoText}"`);
        return await this.callLuaFunction('set_lower_third_text', lowerThirdNumber, nameText, infoText);
    }

    /**
     * Set only the name text for a Lower Third
     */
    async setName(lowerThirdNumber, nameText) {
        console.log(`📝 Setting LT${lowerThirdNumber} name: "${nameText}"`);
        return await this.callLuaFunction('set_lower_third_name', lowerThirdNumber, nameText);
    }

    /**
     * Set only the info text for a Lower Third
     */
    async setInfo(lowerThirdNumber, infoText) {
        console.log(`📝 Setting LT${lowerThirdNumber} info: "${infoText}"`);
        return await this.callLuaFunction('set_lower_third_info', lowerThirdNumber, infoText);
    }

    /**
     * Show a Lower Third with text
     */
    async show(lowerThirdNumber, nameText = '', infoText = '') {
        console.log(`👁️ Showing LT${lowerThirdNumber}: "${nameText}" - "${infoText}"`);
        return await this.callLuaFunction('show_lower_third', lowerThirdNumber, nameText, infoText);
    }

    /**
     * Hide a specific Lower Third
     */
    async hide(lowerThirdNumber) {
        console.log(`🙈 Hiding LT${lowerThirdNumber}`);
        return await this.callLuaFunction('hide_lower_third', lowerThirdNumber);
    }

    /**
     * Toggle a Lower Third on/off
     */
    async toggle(lowerThirdNumber, enabled) {
        console.log(`🔄 Toggling LT${lowerThirdNumber}: ${enabled ? 'ON' : 'OFF'}`);
        return await this.callLuaFunction('toggle_lower_third', lowerThirdNumber, enabled);
    }

    /**
     * Clear text for a specific Lower Third
     */
    async clearText(lowerThirdNumber) {
        console.log(`🧹 Clearing LT${lowerThirdNumber} text`);
        return await this.callLuaFunction('clear_lower_third_text', lowerThirdNumber);
    }

    /**
     * Hide all Lower Thirds
     */
    async hideAll() {
        console.log('🙈 Hiding all Lower Thirds');
        return await this.callLuaFunction('hide_all_lower_thirds');
    }

    /**
     * Clear all Lower Third texts
     */
    async clearAllTexts() {
        console.log('🧹 Clearing all Lower Third texts');
        return await this.callLuaFunction('clear_all_lower_third_texts');
    }
}

// Example usage
async function example() {
    const controller = new LowerThirdController();

    try {
        // Connect to OBS (adjust address and password as needed)
        await controller.connect('ws://localhost:4455', '');

        // Example 1: Set text and show Lower Third 1
        await controller.show(1, 'John Doe', 'Software Engineer');
        await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds

        // Example 2: Update only the info text
        await controller.setInfo(1, 'Senior Developer');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Example 3: Show Lower Third 2 with different text
        await controller.show(2, 'Jane Smith', 'Product Manager');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Example 4: Hide Lower Third 1
        await controller.hide(1);
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Example 5: Clear all texts and hide all
        await controller.clearAllTexts();
        await controller.hideAll();

    } catch (error) {
        console.error('❌ Example failed:', error.message);
    } finally {
        await controller.disconnect();
    }
}

// Export the class for use in other modules
module.exports = LowerThirdController;

// Run example if this file is executed directly
if (require.main === module) {
    example().catch(console.error);
}
