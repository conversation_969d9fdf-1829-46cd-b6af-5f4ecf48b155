/**
 * Lower Thirds WebSocket Control
 *
 * Node.js functions to control OBS Lower Thirds via OBS WebSocket
 * Works with the Python script running in OBS that listens for custom events
 */

const OBSWebSocket = require('obs-websocket-js').default;

class LowerThirdsController {
    constructor(host = 'localhost', port = 4455, password = '') {
        this.host = host;
        this.port = port;
        this.password = password;
        this.obs = new OBSWebSocket();
        this.connected = false;
    }

    /**
     * Connect to OBS WebSocket
     */
    async connect() {
        try {
            await this.obs.connect(`ws://${this.host}:${this.port}`, this.password);
            this.connected = true;
            console.log('✅ Connected to OBS WebSocket');

            this.obs.on('ConnectionClosed', () => {
                this.connected = false;
                console.log('❌ OBS WebSocket connection closed');
            });

            return true;
        } catch (error) {
            console.error('❌ Failed to connect to OBS WebSocket:', error.message);
            return false;
        }
    }

    /**
     * Disconnect from OBS WebSocket
     */
    async disconnect() {
        if (this.connected) {
            await this.obs.disconnect();
            this.connected = false;
            console.log('🔌 Disconnected from OBS WebSocket');
        }
    }

    /**
     * Send custom event to OBS WebSocket for Lower Third control
     */
    async sendLowerThirdEvent(eventData) {
        if (!this.connected) {
            throw new Error('Not connected to OBS WebSocket');
        }

        try {
            await this.obs.call('BroadcastCustomEvent', {
                eventType: 'LowerThirdControl',
                eventData: eventData
            });
            return true;
        } catch (error) {
            console.error('❌ Failed to send custom event:', error.message);
            return false;
        }
    }

    /**
     * Set text for a Lower Third (without changing visibility)
     */
    async setText(number, name, info) {
        try {
            const eventData = {
                number: number,
                name: name,
                info: info,
                action: 'set'
            };

            const result = await this.sendLowerThirdEvent(eventData);
            if (result) {
                console.log(`📝 Set LT${number} text: "${name}" - "${info}"`);
            }
            return result;
        } catch (error) {
            console.error(`❌ Failed to set text for LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Show a Lower Third with text
     */
    async show(number, name = '', info = '') {
        try {
            const eventData = {
                number: number,
                name: name,
                info: info,
                action: 'show'
            };

            const result = await this.sendLowerThirdEvent(eventData);
            if (result) {
                console.log(`👁️ Showing LT${number}: "${name}" - "${info}"`);
            }
            return result;
        } catch (error) {
            console.error(`❌ Failed to show LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Hide a Lower Third
     */
    async hide(number) {
        try {
            const eventData = {
                number: number,
                action: 'hide'
            };

            const result = await this.sendLowerThirdEvent(eventData);
            if (result) {
                console.log(`🙈 Hiding LT${number}`);
            }
            return result;
        } catch (error) {
            console.error(`❌ Failed to hide LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Toggle a Lower Third visibility
     */
    async toggle(number) {
        try {
            const eventData = {
                number: number,
                action: 'toggle'
            };

            const result = await this.sendLowerThirdEvent(eventData);
            if (result) {
                console.log(`🔄 Toggling LT${number}`);
            }
            return result;
        } catch (error) {
            console.error(`❌ Failed to toggle LT${number}:`, error.message);
            return false;
        }
    }

    /**
     * Test connection to OBS WebSocket
     */
    async testConnection() {
        try {
            if (!this.connected) {
                const connected = await this.connect();
                if (!connected) {
                    return false;
                }
            }

            // Test by sending a simple event
            const result = await this.sendLowerThirdEvent({
                number: 1,
                name: '',
                info: '',
                action: 'set'
            });

            if (result) {
                console.log('✅ Connection to OBS WebSocket successful');
                console.log('💡 Make sure the Python script "lower-thirds-websocket-control.py" is loaded in OBS');
                return true;
            } else {
                console.log('❌ Failed to send test event');
                return false;
            }
        } catch (error) {
            console.error('❌ Connection test failed:', error.message);
            console.log('💡 Make sure:');
            console.log('   1. OBS Studio is running');
            console.log('   2. OBS WebSocket is enabled');
            console.log('   3. Python script "lower-thirds-websocket-control.py" is loaded');
            return false;
        }
    }
}

// Example usage and testing
async function example() {
    console.log('🚀 Lower Thirds WebSocket Control Example');
    console.log('');

    const controller = new LowerThirdsController();

    try {
        // Test connection
        const connected = await controller.testConnection();
        if (!connected) {
            return;
        }

        console.log('');
        console.log('📋 Running example sequence...');

        // Example 1: Show Lower Third 1
        await controller.show(1, 'John Doe', 'Software Engineer');
        await sleep(3000);

        // Example 2: Update text without hiding
        await controller.setText(1, 'John Doe', 'Senior Developer');
        await sleep(2000);

        // Example 3: Show Lower Third 2
        await controller.show(2, 'Jane Smith', 'Product Manager');
        await sleep(3000);

        // Example 4: Hide Lower Third 1
        await controller.hide(1);
        await sleep(2000);

        // Example 5: Toggle Lower Third 2
        await controller.toggle(2); // Hide
        await sleep(1000);
        await controller.toggle(2); // Show
        await sleep(2000);

        // Example 6: Hide all
        await controller.hide(1);
        await controller.hide(2);
        await controller.hide(3);
        await controller.hide(4);

        console.log('');
        console.log('✅ Example completed successfully!');

    } catch (error) {
        console.error('❌ Example failed:', error.message);
    } finally {
        await controller.disconnect();
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Export for use as module
module.exports = LowerThirdsController;

// Run example if executed directly
if (require.main === module) {
    example().catch(console.error);
}
