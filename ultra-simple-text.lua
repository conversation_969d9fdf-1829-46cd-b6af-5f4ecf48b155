-- Script ultra-simple pour changer le texte d'un bandeau
-- Modifie directement localStorage comme le panneau de contrôle

-- Configuration
local BANDEAU_NUMERO = 1
local NOUVEAU_NOM = "Test Nom"
local NOUVELLE_INFO = "Test Info"

function script_path()
    local str = debug.getinfo(2, "S").source:sub(2)
    return str:match("(.*/)")
end

function changer_texte()
    print("=== Changement de texte ===")
    print("Bandeau: " .. BANDEAU_NUMERO)
    print("Nom: " .. NOUVEAU_NOM)
    print("Info: " .. NOUVELLE_INFO)
    
    -- Créer un fichier JavaScript qui simule le panneau de contrôle
    local js_file = script_path() .. '../common/js/temp_control.js'
    
    local js_content = [[
// Simulation du panneau de contrôle pour changer le texte
console.log('Changement de texte via script Lua');

// Simuler localStorage comme le panneau de contrôle
if (typeof(Storage) !== "undefined") {
    localStorage.setItem('alt_]] .. BANDEAU_NUMERO .. [[_name', ']] .. NOUVEAU_NOM .. [[');
    localStorage.setItem('alt_]] .. BANDEAU_NUMERO .. [[_info', ']] .. NOUVELLE_INFO .. [[');
    localStorage.setItem('alt_]] .. BANDEAU_NUMERO .. [[_switch', 'true');
    console.log('Texte sauvegardé dans localStorage');
}

// Envoyer via BroadcastChannel
if (typeof BroadcastChannel !== 'undefined') {
    const bc = new BroadcastChannel('obs-lower-thirds-channel');
    const message = {
        alt_]] .. BANDEAU_NUMERO .. [[_name: ']] .. NOUVEAU_NOM .. [[',
        alt_]] .. BANDEAU_NUMERO .. [[_info: ']] .. NOUVELLE_INFO .. [[',
        alt_]] .. BANDEAU_NUMERO .. [[_switch: 'true',
        timestamp: Date.now()
    };
    bc.postMessage(message);
    console.log('Message envoyé:', message);
}
]]

    local file = io.open(js_file, "w")
    if file then
        file:write(js_content)
        file:close()
        print("Fichier créé: " .. js_file)
        print("ÉTAPES:")
        print("1. Ouvrez le panneau de contrôle dans OBS")
        print("2. Ouvrez la console du navigateur (F12)")
        print("3. Copiez-collez le contenu du fichier " .. js_file)
        print("4. Appuyez sur Entrée")
    else
        print("ERREUR: Impossible de créer le fichier")
    end
end

function script_description()
    return "Script ultra-simple pour changer le texte.\nModifiez les variables et appelez changer_texte()"
end

function script_load(settings)
    print("Script chargé. Appelez changer_texte() pour changer le texte.")
end

-- Appel automatique au chargement pour test
changer_texte()
